/* ID Dropdown Component Styles */
/* Inherits from base dropdown and adds ID-specific styling */

@import '../base-dropdown/base-dropdown.component.scss';

/* ID-specific dropdown styling - EXACT from initial-input component */
.id-dropdown {
  /* Inherits all base dropdown styles */
}

.id-dropdown-item {
  /* Inherits base dropdown-item styles */
  font-weight: 500; /* Slightly bolder for ID values */
}

.id-dropdown-item:hover {
  background-color: #e3f2fd;
  color: #1976d2;
}

.id-dropdown-empty {
  /* Inherits base dropdown-empty styles */
}

/* Validation styling for invalid input */
.invalid-input {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1) !important;
}

.invalid-input:focus {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
}

/* Validation message styling */
.validation-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  font-family: 'Poppins', sans-serif;
  display: flex;
  align-items: center;
  gap: 4px;
}

.validation-message::before {
  content: '⚠';
  font-size: 14px;
}

/* Container adjustments for validation */
.dropdown-input-container:has(.invalid-input) {
  margin-bottom: 4px;
}

/* ID input specific styling */
.dropdown-input-container input[type="text"] {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Focus state for ID input */
.dropdown-input-container input[type="text"]:focus {
  outline: none;
  border-color: #283A97;
  box-shadow: 0 0 0 2px rgba(40, 58, 151, 0.1);
}

/* Disabled state styling */
.dropdown-input-container input[type="text"]:disabled {
  background-color: #f1f3f4;
  color: #5f6368;
  cursor: not-allowed;
  border-color: #dadce0;
}

/* Required field indicator */
.dropdown-input-container input[required] {
  /* Add subtle indicator for required fields */
}

.dropdown-input-container input[required]:invalid {
  /* Style for invalid required fields */
}

/* Responsive adjustments for ID dropdown */
@media (max-width: 768px) {
  .validation-message {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .validation-message {
    font-size: 10px;
  }
  
  .id-dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .invalid-input {
    border-width: 2px;
  }
  
  .validation-message {
    font-weight: bold;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .validation-message {
    color: #ff6b6b;
  }
  
  .invalid-input {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.1);
  }
  
  .invalid-input:focus {
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.25);
  }
}

/* Animation for validation message */
.validation-message {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper spacing in form contexts */
.form-field .dropdown-input-container {
  width: 100%;
}

.form-field .validation-message {
  margin-left: 0;
  margin-right: 0;
}

/* Loading state specific to ID dropdown */
.id-dropdown .dropdown-loading {
  color: #1976d2;
}

/* Error state specific to ID dropdown */
.id-dropdown .dropdown-error {
  background-color: #ffebee;
  border-color: #ffcdd2;
  color: #c62828;
}
