import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../../environments/environment';

// Import base dropdown and models
import { BaseDropdownComponent } from '../base-dropdown/base-dropdown.component';
import {
  DropdownOption,
  TypeDropdownConfig
} from '../../../../core/models/dropdown.models';

@Component({
  selector: 'app-type-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './type-dropdown.component.html',
  styleUrl: './type-dropdown.component.scss'
})
export class TypeDropdownComponent extends BaseDropdownComponent implements OnInit {
  @Input() override config: TypeDropdownConfig = {};

  // Cache key for type options
  private readonly CACHE_KEY = 'fieldType';

  override ngOnInit() {
    // Set dropdown type
    this.dropdownType = 'type';
    
    // Set default configuration for Type dropdown
    this.config = {
      searchEnabled: true,
      cacheEnabled: true, // Types are relatively static, cache them
      clientSideFiltering: true, // Use client-side filtering for better performance
      limit: 100,
      placeholder: 'Search field types',
      emptyMessage: 'No types found',
      loadingMessage: 'Loading types...',
      searchDelay: 200, // Faster response for cached data
      ...this.config
    };

    super.ngOnInit();
    
    // Preload type options for better performance
    this.preloadTypeOptions();
  }

  /**
   * Override perform search for Type-specific logic with caching
   */
  protected override performSearch(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Use client-side filtering if data is cached
    if (this.apiCache[this.CACHE_KEY]) {
      this.filterCachedOptions(searchTerm);
    } else {
      // Load all and then filter (fallback)
      this.loadAllAndFilter(searchTerm);
    }
  }

  /**
   * Override load all options for Type-specific API calls with caching
   */
  protected override loadAllOptions(): void {
    // Use cached data if available
    if (this.apiCache[this.CACHE_KEY]) {
      this.state.filteredOptions = this.apiCache[this.CACHE_KEY].data;
      this.state.options = [...this.state.filteredOptions];
      this.state.isOpen = true;
      this.cdr.detectChanges();
      return;
    }

    // Load from API if not cached
    this.loadTypesFromApi();
  }

  /**
   * Preload type options for better performance
   */
  private preloadTypeOptions(): void {
    if (!this.apiCache[this.CACHE_KEY]) {
      this.loadTypesFromApi(false); // Load without opening dropdown
    }
  }

  /**
   * Load types from API
   */
  private loadTypesFromApi(openDropdown: boolean = true): void {
    this.state.isLoading = true;
    this.state.error = undefined;

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        
        if (Array.isArray(response)) {
          const options = response.map(item => ({
            ROW_ID: item.ROW_ID,
            ...item
          }));

          // Cache the results
          this.apiCache[this.CACHE_KEY] = {
            data: options,
            timestamp: Date.now(),
            expiresIn: 30 * 60 * 1000 // 30 minutes cache
          };

          this.state.filteredOptions = options;
          this.state.options = [...options];
        } else {
          this.state.filteredOptions = [];
          this.state.options = [];
        }

        if (openDropdown) {
          this.state.isOpen = true;
        }
        
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.options = [];
        this.state.error = 'Failed to load field types';
        
        if (openDropdown) {
          this.state.isOpen = true;
        }
        
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Filter cached options client-side
   */
  private filterCachedOptions(searchTerm: string): void {
    const cachedData = this.apiCache[this.CACHE_KEY];
    if (!cachedData) {
      this.loadAllAndFilter(searchTerm);
      return;
    }

    const term = searchTerm.toLowerCase();
    this.state.filteredOptions = cachedData.data.filter(option =>
      option.ROW_ID.toLowerCase().includes(term)
    );
    
    this.state.isOpen = true;
    this.cdr.detectChanges();
  }

  /**
   * Load all types and filter them (fallback method)
   */
  private loadAllAndFilter(searchTerm: string): void {
    this.state.isLoading = true;
    this.state.error = undefined;

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        
        if (Array.isArray(response)) {
          const options = response.map(item => ({
            ROW_ID: item.ROW_ID,
            ...item
          }));

          // Cache the results
          this.apiCache[this.CACHE_KEY] = {
            data: options,
            timestamp: Date.now(),
            expiresIn: 30 * 60 * 1000 // 30 minutes cache
          };

          // Filter the results
          const term = searchTerm.toLowerCase();
          this.state.filteredOptions = options.filter(option =>
            option.ROW_ID.toLowerCase().includes(term)
          );
          
          this.state.options = options;
        } else {
          this.state.filteredOptions = [];
          this.state.options = [];
        }

        this.state.isOpen = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.options = [];
        this.state.error = 'Failed to load field types';
        this.state.isOpen = true;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Get display text for Type options
   */
  protected override getOptionDisplayText(option: DropdownOption): string {
    return option.ROW_ID || '';
  }

  /**
   * Check if cache is valid
   */
  private isCacheValid(): boolean {
    const cachedData = this.apiCache[this.CACHE_KEY];
    if (!cachedData) return false;

    const now = Date.now();
    const expiresAt = cachedData.timestamp + (cachedData.expiresIn || 30 * 60 * 1000);
    
    return now < expiresAt;
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    delete this.apiCache[this.CACHE_KEY];
  }

  /**
   * Refresh data by clearing cache and reloading
   */
  refreshData(): void {
    this.clearCache();
    this.loadTypesFromApi();
  }
}
