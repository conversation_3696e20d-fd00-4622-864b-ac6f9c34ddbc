// Dropdown-related interfaces and types for the dynamic-form module

/**
 * Represents a dropdown option with flexible structure
 */
export interface DropdownOption {
  ROW_ID: string;
  ID?: string;
  [key: string]: any; // Allow additional properties for different option types
}

/**
 * Configuration for dropdown behavior and API calls
 */
export interface DropdownConfig {
  apiEndpoint?: string;
  queryBuilderId?: string;
  searchEnabled?: boolean;
  cacheEnabled?: boolean;
  clientSideFiltering?: boolean;
  limit?: number;
  placeholder?: string;
  emptyMessage?: string;
  loadingMessage?: string;
  searchDelay?: number; // Debounce delay in milliseconds
}

/**
 * Dropdown state management interface
 */
export interface DropdownState {
  isOpen: boolean;
  isLoading: boolean;
  options: DropdownOption[];
  filteredOptions: DropdownOption[];
  searchTerm: string;
  selectedOption?: DropdownOption;
  error?: string;
}

/**
 * Dropdown event data for communication between components
 */
export interface DropdownEventData {
  fieldName: string;
  value: any;
  option?: DropdownOption;
  dropdownType: DropdownType;
}

/**
 * Types of dropdowns supported in the dynamic-form system
 */
export type DropdownType = 'id' | 'type' | 'foreignKey' | 'regular';

/**
 * API payload structure for dropdown data requests
 */
export interface DropdownApiPayload {
  _select: string[];
  _limit?: number;
  [key: string]: any; // Allow additional search criteria
}

/**
 * Search criteria for different dropdown types
 */
export interface DropdownSearchCriteria {
  searchTerm?: string;
  fieldName?: string;
  foreignKey?: string;
  tableName?: string;
  screenName?: string;
}

/**
 * Cache entry for dropdown options
 */
export interface DropdownCacheEntry {
  data: DropdownOption[];
  timestamp: number;
  expiresIn?: number; // Cache expiration time in milliseconds
}

/**
 * Dropdown validation result
 */
export interface DropdownValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

/**
 * Base interface for dropdown component inputs
 */
export interface BaseDropdownInputs {
  fieldName: string;
  value?: any;
  disabled?: boolean;
  readonly?: boolean;
  placeholder?: string;
  required?: boolean;
  config?: DropdownConfig;
}

/**
 * Base interface for dropdown component outputs
 */
export interface BaseDropdownOutputs {
  valueChange: DropdownEventData;
  optionSelected: DropdownOption;
  searchTermChange: string;
  dropdownToggle: boolean;
  validationChange: DropdownValidationResult;
}

/**
 * ID dropdown specific configuration
 */
export interface IdDropdownConfig extends DropdownConfig {
  tableName?: string;
  screenName?: string;
  validationEnabled?: boolean;
  theme?: DropdownTheme;
  accessibility?: DropdownAccessibility;
}

/**
 * Type dropdown specific configuration
 */
export interface TypeDropdownConfig extends DropdownConfig {
  typeCategory?: string; // For different type categories if needed
  theme?: DropdownTheme;
  accessibility?: DropdownAccessibility;
}

/**
 * Foreign key dropdown specific configuration
 */
export interface ForeignKeyDropdownConfig extends DropdownConfig {
  foreignKeyType?: string;
  relatedEntity?: string;
  theme?: DropdownTheme;
  accessibility?: DropdownAccessibility;
}

/**
 * Regular dropdown specific configuration
 */
export interface RegularDropdownConfig extends DropdownConfig {
  foreignKey: string;
  displayFields?: string[]; // Fields to display in dropdown options
  valueField?: string; // Field to use as the value
  theme?: DropdownTheme;
  accessibility?: DropdownAccessibility;
}

/**
 * Dropdown field context for complex field name handling
 */
export interface DropdownFieldContext {
  originalFieldName: string;
  displayFieldName: string;
  groupIndex?: number;
  multiIndex?: number;
  nestedGroupIndex?: number;
  isNested?: boolean;
  isMulti?: boolean;
  isGrouped?: boolean;
}

/**
 * Dropdown API response structure
 */
export interface DropdownApiResponse {
  success: boolean;
  data: DropdownOption[];
  error?: string;
  totalCount?: number;
}

/**
 * Dropdown component theme configuration
 */
export interface DropdownTheme {
  containerClass?: string;
  inputClass?: string;
  buttonClass?: string;
  listClass?: string;
  itemClass?: string;
  emptyClass?: string;
  loadingClass?: string;
  errorClass?: string;
}

/**
 * Dropdown accessibility configuration
 */
export interface DropdownAccessibility {
  ariaLabel?: string;
  ariaDescribedBy?: string;
  role?: string;
  tabIndex?: number;
}

/**
 * Complete dropdown configuration combining all aspects
 */
export interface CompleteDropdownConfig extends DropdownConfig {
  theme?: DropdownTheme;
  accessibility?: DropdownAccessibility;
  fieldContext?: DropdownFieldContext;
}
