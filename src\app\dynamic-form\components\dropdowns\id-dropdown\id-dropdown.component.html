<div [class]="getContainerClass()">
  <!-- Input field with search functionality and validation styling -->
  <input 
    [formControl]="inputControl"
    [id]="fieldName" 
    type="text" 
    [class]="getInputClass()"
    (focus)="onInputFocus()" 
    (blur)="onInputBlur()"
    [disabled]="disabled || readonly"
    [placeholder]="placeholder"
    [required]="required"
    [attr.aria-label]="config.accessibility?.ariaLabel || 'Enter ID'"
    [attr.aria-describedby]="config.accessibility?.ariaDescribedBy"
    [attr.role]="config.accessibility?.role || 'combobox'"
    [attr.aria-expanded]="state.isOpen"
    [attr.aria-autocomplete]="'list'"
    [attr.aria-invalid]="showValidation"
    [attr.tabindex]="config.accessibility?.tabIndex || 0" />
  
  <!-- Arrow button to toggle dropdown -->
  <button 
    type="button" 
    class="dropdown-arrow-btn" 
    (click)="toggleDropdown()" 
    [disabled]="disabled || readonly"
    [matTooltip]="'Show ID suggestions'"
    [attr.aria-label]="'Toggle ID dropdown options'">
    <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
  </button>
  
  <!-- Dropdown list for filtered results -->
  @if (state.isOpen) {
    <div class="dropdown-list id-dropdown" [class]="config.theme?.listClass || ''">
      <!-- Loading state -->
      @if (state.isLoading) {
        <div class="dropdown-loading" [class]="config.theme?.loadingClass || ''">
          {{ config.loadingMessage || 'Loading IDs...' }}
        </div>
      }
      
      <!-- ID options list -->
      @else if (state.filteredOptions && state.filteredOptions.length > 0) {
        @for (option of state.filteredOptions; track trackByOptionId($index, option)) {
          <div 
            class="dropdown-item id-dropdown-item" 
            [class]="config.theme?.itemClass || ''"
            (click)="selectOption(option)"
            [attr.role]="'option'"
            [attr.aria-selected]="state.selectedOption?.ID === option.ID">
            {{ option.ID }}
          </div>
        }
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty id-dropdown-empty" [class]="config.theme?.emptyClass || ''">
          {{ config.emptyMessage || 'No IDs found' }}
        </div>
      }
      
      <!-- Error state -->
      @if (state.error) {
        <div class="dropdown-error" [class]="config.theme?.errorClass || ''">
          {{ state.error }}
        </div>
      }
    </div>
  }
</div>

<!-- Validation message -->
@if (showValidation && validationEnabled) {
  <div class="validation-message" role="alert" aria-live="polite">
    Please enter a valid ID
  </div>
}
