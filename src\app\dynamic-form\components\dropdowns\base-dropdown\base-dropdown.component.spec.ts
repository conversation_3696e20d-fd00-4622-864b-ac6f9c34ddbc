import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { BaseDropdownComponent } from './base-dropdown.component';
import { DropdownEventData, DropdownOption } from '../../../../core/models/dropdown.models';

describe('BaseDropdownComponent', () => {
  let component: BaseDropdownComponent;
  let fixture: ComponentFixture<BaseDropdownComponent>;
  let httpMock: HttpTestingController;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        BaseDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(BaseDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    
    // Set required inputs
    component.fieldName = 'testField';
    component.dropdownType = 'regular';
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default configuration', () => {
    component.ngOnInit();
    
    expect(component.config.searchEnabled).toBe(true);
    expect(component.config.cacheEnabled).toBe(true);
    expect(component.config.searchDelay).toBe(300);
    expect(component.inputControl).toBeDefined();
  });

  it('should setup input control with initial value', () => {
    component.value = 'test value';
    component.ngOnInit();
    
    expect(component.inputControl.value).toBe('test value');
    expect(component.state.searchTerm).toBe('test value');
  });

  it('should disable input control when disabled or readonly', () => {
    component.disabled = true;
    component.ngOnInit();
    
    expect(component.inputControl.disabled).toBe(true);
  });

  it('should handle input changes with debouncing', (done) => {
    component.ngOnInit();
    spyOn(component, 'performSearch');
    
    component.inputControl.setValue('test');
    
    setTimeout(() => {
      expect(component.performSearch).toHaveBeenCalledWith('test');
      done();
    }, 350);
  });

  it('should emit value change events', () => {
    spyOn(component.valueChange, 'emit');
    component.ngOnInit();
    
    const expectedEventData: DropdownEventData = {
      fieldName: 'testField',
      value: 'test value',
      dropdownType: 'regular'
    };
    
    component.inputControl.setValue('test value');
    
    expect(component.valueChange.emit).toHaveBeenCalledWith(expectedEventData);
  });

  it('should open dropdown on input focus', () => {
    component.ngOnInit();
    spyOn(component, 'loadAllOptions');
    
    component.onInputFocus();
    
    expect(component.loadAllOptions).toHaveBeenCalled();
  });

  it('should close dropdown on input blur with delay', (done) => {
    component.ngOnInit();
    component.state.isOpen = true;
    
    component.onInputBlur();
    
    setTimeout(() => {
      expect(component.state.isOpen).toBe(false);
      done();
    }, 250);
  });

  it('should toggle dropdown visibility', () => {
    component.ngOnInit();
    spyOn(component, 'openDropdown');
    spyOn(component, 'closeDropdown');
    
    // Test opening dropdown
    component.state.isOpen = false;
    component.toggleDropdown();
    expect(component.openDropdown).toHaveBeenCalled();
    
    // Test closing dropdown
    component.state.isOpen = true;
    component.toggleDropdown();
    expect(component.closeDropdown).toHaveBeenCalled();
  });

  it('should not interact when disabled', () => {
    component.disabled = true;
    component.ngOnInit();
    spyOn(component, 'openDropdown');
    
    component.toggleDropdown();
    component.onInputFocus();
    
    expect(component.openDropdown).not.toHaveBeenCalled();
  });

  it('should select option and emit events', () => {
    component.ngOnInit();
    spyOn(component.optionSelected, 'emit');
    spyOn(component.valueChange, 'emit');
    spyOn(component, 'closeDropdown');
    
    const testOption: DropdownOption = {
      ROW_ID: 'test-id',
      name: 'Test Option'
    };
    
    component.selectOption(testOption);
    
    expect(component.state.selectedOption).toBe(testOption);
    expect(component.inputControl.value).toBe('test-id');
    expect(component.optionSelected.emit).toHaveBeenCalledWith(testOption);
    expect(component.closeDropdown).toHaveBeenCalled();
  });

  it('should filter options client-side when enabled', () => {
    component.config.clientSideFiltering = true;
    component.state.options = [
      { ROW_ID: 'option1', name: 'First Option' },
      { ROW_ID: 'option2', name: 'Second Option' },
      { ROW_ID: 'option3', name: 'Third Option' }
    ];
    component.ngOnInit();
    
    component.filterOptions('first');
    
    expect(component.state.filteredOptions.length).toBe(1);
    expect(component.state.filteredOptions[0].ROW_ID).toBe('option1');
  });

  it('should get correct display text for options', () => {
    const option1: DropdownOption = { ROW_ID: 'test-id' };
    const option2: DropdownOption = { ROW_ID: '', name: 'Test Name' };
    
    expect(component.getOptionDisplayText(option1)).toBe('test-id');
    expect(component.getOptionDisplayText(option2)).toBe('Test Name');
  });

  it('should apply correct CSS classes', () => {
    component.disabled = true;
    component.config.theme = {
      inputClass: 'custom-input',
      containerClass: 'custom-container'
    };
    
    const inputClass = component.getInputClass();
    const containerClass = component.getContainerClass();
    
    expect(inputClass).toContain('disabled');
    expect(inputClass).toContain('custom-input');
    expect(containerClass).toContain('custom-container');
  });

  it('should track options by ID for ngFor optimization', () => {
    const option: DropdownOption = { ROW_ID: 'test-id', ID: 'alt-id' };
    
    const trackResult = component.trackByOptionId(0, option);
    
    expect(trackResult).toBe('test-id');
  });

  it('should clear search timeout on destroy', () => {
    component.ngOnInit();
    spyOn(window, 'clearTimeout');
    
    // Simulate setting a timeout
    component['searchTimeout'] = setTimeout(() => {}, 1000);
    
    component.ngOnDestroy();
    
    expect(clearTimeout).toHaveBeenCalled();
  });

  it('should handle empty options gracefully', () => {
    component.state.options = [];
    component.ngOnInit();
    
    component.filterOptions('test');
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.isOpen).toBe(true);
  });

  it('should emit dropdown toggle events', () => {
    spyOn(component.dropdownToggle, 'emit');
    component.ngOnInit();
    
    component.openDropdown();
    expect(component.dropdownToggle.emit).toHaveBeenCalledWith(true);
    
    component.closeDropdown();
    expect(component.dropdownToggle.emit).toHaveBeenCalledWith(false);
  });
});
