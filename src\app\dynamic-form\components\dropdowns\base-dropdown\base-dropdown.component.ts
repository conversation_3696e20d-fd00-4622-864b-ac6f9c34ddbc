import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, inject, ChangeDetectorRef } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../../environments/environment';

// Import dropdown models
import {
  DropdownOption,
  DropdownConfig,
  DropdownState,
  DropdownEventData,
  DropdownType,
  DropdownApiPayload,
  DropdownSearchCriteria,
  DropdownCacheEntry,
  DropdownValidationResult,
  CompleteDropdownConfig
} from '../../../../core/models/dropdown.models';

@Component({
  selector: 'app-base-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './base-dropdown.component.html',
  styleUrl: './base-dropdown.component.scss'
})
export class BaseDropdownComponent implements OnInit, OnDestroy {
  @Input() fieldName!: string;
  @Input() value: any = '';
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() placeholder: string = '';
  @Input() required: boolean = false;
  @Input() config: CompleteDropdownConfig = {};
  @Input() dropdownType: DropdownType = 'regular';

  @Output() valueChange = new EventEmitter<DropdownEventData>();
  @Output() optionSelected = new EventEmitter<DropdownOption>();
  @Output() searchTermChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() validationChange = new EventEmitter<DropdownValidationResult>();

  // Component state
  state: DropdownState = {
    isOpen: false,
    isLoading: false,
    options: [],
    filteredOptions: [],
    searchTerm: '',
    selectedOption: undefined,
    error: undefined
  };

  // Form control for the input
  inputControl = new FormControl('');

  // Cache for API responses
  protected apiCache: { [key: string]: DropdownCacheEntry } = {};

  // Search timeout for debouncing
  private searchTimeout: any;

  // Injected services
  protected http = inject(HttpClient);
  protected cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    this.initializeComponent();
    this.setupInputControl();
  }

  ngOnDestroy() {
    this.clearSearchTimeout();
  }

  /**
   * Initialize component with default configuration
   */
  protected initializeComponent(): void {
    // Set default configuration
    this.config = {
      searchEnabled: true,
      cacheEnabled: true,
      clientSideFiltering: false,
      limit: 50,
      emptyMessage: 'No options found',
      loadingMessage: 'Loading...',
      searchDelay: 300,
      ...this.config
    };

    // Set initial value
    if (this.value) {
      this.inputControl.setValue(this.value);
      this.state.searchTerm = this.value;
    }

    // Set placeholder
    if (!this.placeholder && this.config.placeholder) {
      this.placeholder = this.config.placeholder;
    }
  }

  /**
   * Setup input control with event listeners
   */
  protected setupInputControl(): void {
    // Handle input changes
    this.inputControl.valueChanges.subscribe(value => {
      this.onInputChange(value || '');
    });

    // Set disabled state
    if (this.disabled || this.readonly) {
      this.inputControl.disable();
    }
  }

  /**
   * Handle input value changes with debouncing
   */
  protected onInputChange(value: string): void {
    this.state.searchTerm = value;
    this.searchTermChange.emit(value);

    // Clear previous timeout
    this.clearSearchTimeout();

    // Set new timeout for search
    this.searchTimeout = setTimeout(() => {
      this.performSearch(value);
    }, this.config.searchDelay || 300);

    // Emit value change
    this.emitValueChange(value);
  }

  /**
   * Handle input focus event
   */
  onInputFocus(): void {
    if (this.disabled || this.readonly) return;

    const currentValue = this.inputControl.value || '';
    if (currentValue.trim() === '') {
      this.loadAllOptions();
    } else {
      this.performSearch(currentValue);
    }
  }

  /**
   * Handle input blur event
   */
  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.closeDropdown();
    }, 200);
  }

  /**
   * Toggle dropdown visibility
   */
  toggleDropdown(): void {
    if (this.disabled || this.readonly) return;

    if (!this.state.isOpen) {
      this.openDropdown();
    } else {
      this.closeDropdown();
    }
  }

  /**
   * Open dropdown and load options
   */
  protected openDropdown(): void {
    this.state.isOpen = true;
    this.dropdownToggle.emit(true);

    const currentValue = this.inputControl.value || '';
    if (currentValue.trim() === '') {
      this.loadAllOptions();
    } else {
      this.performSearch(currentValue);
    }
  }

  /**
   * Close dropdown
   */
  protected closeDropdown(): void {
    this.state.isOpen = false;
    this.dropdownToggle.emit(false);
  }

  /**
   * Perform search operation (to be overridden by child components)
   */
  protected performSearch(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Default implementation - override in child components
    this.filterOptions(searchTerm);
  }

  /**
   * Load all available options (to be overridden by child components)
   */
  protected loadAllOptions(): void {
    // Default implementation - override in child components
    this.state.filteredOptions = this.state.options;
    this.state.isOpen = true;
  }

  /**
   * Filter options client-side
   */
  protected filterOptions(searchTerm: string): void {
    if (!this.config.clientSideFiltering) return;

    const term = searchTerm.toLowerCase();
    this.state.filteredOptions = this.state.options.filter(option =>
      this.getOptionDisplayText(option).toLowerCase().includes(term)
    );
    this.state.isOpen = true;
  }

  /**
   * Select an option from the dropdown
   */
  selectOption(option: DropdownOption): void {
    this.state.selectedOption = option;
    const displayText = this.getOptionDisplayText(option);
    
    this.inputControl.setValue(displayText);
    this.closeDropdown();

    // Emit events
    this.optionSelected.emit(option);
    this.emitValueChange(option.ROW_ID || displayText, option);

    // Trigger change detection
    this.cdr.detectChanges();
  }

  /**
   * Get display text for an option
   */
  protected getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // Use ROW_ID as default display text
    if (option.ROW_ID) {
      return option.ROW_ID;
    }

    // Fallback to first available property
    const keys = Object.keys(option);
    return keys.length > 0 ? option[keys[0]] : '';
  }

  /**
   * Emit value change event
   */
  protected emitValueChange(value: any, option?: DropdownOption): void {
    const eventData: DropdownEventData = {
      fieldName: this.fieldName,
      value: value,
      option: option,
      dropdownType: this.dropdownType
    };

    this.valueChange.emit(eventData);
  }

  /**
   * Clear search timeout
   */
  protected clearSearchTimeout(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = null;
    }
  }

  /**
   * Get CSS classes for the input
   */
  getInputClass(): string {
    const classes = ['form-input', 'dropdown-input'];
    
    if (this.disabled || this.readonly) {
      classes.push('disabled');
    }
    
    if (this.config.theme?.inputClass) {
      classes.push(this.config.theme.inputClass);
    }

    return classes.join(' ');
  }

  /**
   * Get CSS classes for the dropdown container
   */
  getContainerClass(): string {
    const classes = ['dropdown-input-container'];
    
    if (this.config.theme?.containerClass) {
      classes.push(this.config.theme.containerClass);
    }

    return classes.join(' ');
  }

  /**
   * Track function for ngFor optimization
   */
  trackByOptionId(index: number, option: DropdownOption): any {
    return option.ROW_ID || option.ID || index;
  }
}
