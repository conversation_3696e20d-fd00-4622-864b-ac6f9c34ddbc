import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../../environments/environment';

// Import base dropdown and models
import { BaseDropdownComponent } from '../base-dropdown/base-dropdown.component';
import {
  DropdownOption,
  DropdownEventData,
  IdDropdownConfig,
  DropdownValidationResult
} from '../../../../core/models/dropdown.models';

@Component({
  selector: 'app-id-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './id-dropdown.component.html',
  styleUrl: './id-dropdown.component.scss'
})
export class IdDropdownComponent extends BaseDropdownComponent implements OnInit {
  @Input() tableName?: string;
  @Input() screenName?: string;
  @Input() validationEnabled: boolean = true;
  @Input() override config: IdDropdownConfig = {};

  @Output() validationStateChange = new EventEmitter<boolean>();

  // ID-specific validation state
  showValidation: boolean = false;

  override ngOnInit() {
    // Set dropdown type
    this.dropdownType = 'id';
    
    // Set default configuration for ID dropdown
    this.config = {
      searchEnabled: true,
      cacheEnabled: false, // IDs are dynamic, don't cache
      clientSideFiltering: false, // Use server-side filtering
      limit: 100,
      placeholder: 'Enter ID',
      emptyMessage: 'No IDs found',
      loadingMessage: 'Loading IDs...',
      searchDelay: 300,
      validationEnabled: this.validationEnabled,
      tableName: this.tableName,
      screenName: this.screenName,
      ...this.config
    };

    super.ngOnInit();
  }

  /**
   * Override input change to handle validation
   */
  protected override onInputChange(value: string): void {
    // Reset validation when user types
    if (value && value.trim() !== '') {
      this.setValidationState(false);
    }

    super.onInputChange(value);
  }

  /**
   * Override perform search for ID-specific API calls
   */
  protected override performSearch(searchTerm: string): void {
    if (!this.tableName && !this.screenName) {
      this.state.error = 'Table name or screen name is required';
      this.state.isOpen = true;
      return;
    }

    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    this.state.isLoading = true;
    this.state.error = undefined;

    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;

    const payload = {
      ID: {
        CT: searchTerm // CT = Contains operator
      },
      _select: ["ID"],
      _limit: this.config.limit || 20
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        if (Array.isArray(response)) {
          this.state.filteredOptions = response.map(item => ({
            ROW_ID: item.ID,
            ID: item.ID,
            ...item
          }));
        } else {
          this.state.filteredOptions = [];
        }
        this.state.isOpen = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.error = 'Failed to load IDs';
        this.state.isOpen = true;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Override load all options for ID-specific API calls
   */
  protected override loadAllOptions(): void {
    if (!this.tableName && !this.screenName) {
      this.state.error = 'Table name or screen name is required';
      this.state.isOpen = true;
      return;
    }

    this.state.isLoading = true;
    this.state.error = undefined;

    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    
    const payload = {
      _select: ["ID"],
      _limit: this.config.limit || 100
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        if (Array.isArray(response)) {
          this.state.filteredOptions = response.map(item => ({
            ROW_ID: item.ID,
            ID: item.ID,
            ...item
          }));
          this.state.options = [...this.state.filteredOptions];
        } else {
          this.state.filteredOptions = [];
          this.state.options = [];
        }
        this.state.isOpen = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.options = [];
        this.state.error = 'Failed to load IDs';
        this.state.isOpen = true;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Override select option to handle ID-specific logic
   */
  override selectOption(option: DropdownOption): void {
    super.selectOption(option);
    
    // Reset validation state when a valid ID is selected
    this.setValidationState(false);
  }

  /**
   * Get display text for ID options
   */
  protected override getOptionDisplayText(option: DropdownOption): string {
    return option.ID || option.ROW_ID || '';
  }

  /**
   * Extract query builder ID from table/screen name
   */
  private extractQueryBuilderId(): string {
    const nameToUse = this.tableName || this.screenName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse || '';
  }

  /**
   * Set validation state and emit changes
   */
  setValidationState(isInvalid: boolean): void {
    this.showValidation = isInvalid;
    this.validationStateChange.emit(isInvalid);
    
    const validationResult: DropdownValidationResult = {
      isValid: !isInvalid,
      errorMessage: isInvalid ? 'Please select a valid ID' : undefined
    };
    
    this.validationChange.emit(validationResult);
  }

  /**
   * Get input CSS classes with validation state
   */
  override getInputClass(): string {
    const baseClasses = super.getInputClass();
    return this.showValidation ? `${baseClasses} invalid-input` : baseClasses;
  }

  /**
   * Validate current input value
   */
  validateInput(): boolean {
    const currentValue = this.inputControl.value;
    const isValid = !this.required || (currentValue && currentValue.trim() !== '');

    if (!isValid) {
      this.setValidationState(true);
    }

    return !!isValid;
  }

  /**
   * Clear the input and reset state
   */
  clearInput(): void {
    this.inputControl.setValue('');
    this.state.selectedOption = undefined;
    this.state.searchTerm = '';
    this.setValidationState(false);
    this.closeDropdown();
  }
}
