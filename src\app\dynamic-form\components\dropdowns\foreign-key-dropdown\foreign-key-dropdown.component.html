<div [class]="getContainerClass()">
  <!-- Input field with search functionality -->
  <input 
    [formControl]="inputControl"
    [id]="fieldName" 
    type="text" 
    [class]="getInputClass()"
    (focus)="onInputFocus()" 
    (blur)="onInputBlur()"
    [disabled]="disabled || readonly"
    [placeholder]="placeholder"
    [attr.aria-label]="config.accessibility?.ariaLabel || 'Search form definitions'"
    [attr.aria-describedby]="config.accessibility?.ariaDescribedBy"
    [attr.role]="config.accessibility?.role || 'combobox'"
    [attr.aria-expanded]="state.isOpen"
    [attr.aria-autocomplete]="'list'"
    [attr.tabindex]="config.accessibility?.tabIndex || 0" />
  
  <!-- Arrow button to toggle dropdown -->
  <button 
    type="button" 
    class="dropdown-arrow-btn" 
    (click)="toggleDropdown()" 
    [disabled]="disabled || readonly"
    [matTooltip]="'Show form definition suggestions'"
    [attr.aria-label]="'Toggle foreign key dropdown options'">
    <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
  </button>
  
  <!-- Dropdown list for filtered results -->
  @if (state.isOpen) {
    <div class="dropdown-list foreign-key-dropdown" [class]="config.theme?.listClass || ''">
      <!-- Loading state -->
      @if (state.isLoading) {
        <div class="dropdown-loading" [class]="config.theme?.loadingClass || ''">
          {{ config.loadingMessage || 'Loading form definitions...' }}
        </div>
      }
      
      <!-- Foreign key options list -->
      @else if (state.filteredOptions && state.filteredOptions.length > 0) {
        @for (option of state.filteredOptions; track trackByOptionId($index, option)) {
          <div 
            class="dropdown-item foreign-key-dropdown-item" 
            [class]="config.theme?.itemClass || ''"
            (click)="selectOption(option)"
            [attr.role]="'option'"
            [attr.aria-selected]="state.selectedOption?.ROW_ID === option.ROW_ID">
            {{ option.ROW_ID }}
          </div>
        }
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty foreign-key-dropdown-empty" [class]="config.theme?.emptyClass || ''">
          {{ config.emptyMessage || 'No form definitions found' }}
        </div>
      }
      
      <!-- Error state -->
      @if (state.error) {
        <div class="dropdown-error" [class]="config.theme?.errorClass || ''">
          {{ state.error }}
        </div>
      }
    </div>
  }
</div>
