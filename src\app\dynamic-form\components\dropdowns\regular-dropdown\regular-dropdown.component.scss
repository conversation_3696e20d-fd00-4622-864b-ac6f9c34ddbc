/* Regular Dropdown Component Styles */
/* Inherits from base dropdown and adds Regular dropdown-specific styling */

@import '../base-dropdown/base-dropdown.component.scss';

/* Regular dropdown-specific styling */
.regular-dropdown {
  /* Inherits all base dropdown styles */
}

.regular-dropdown-item {
  /* Inherits base dropdown-item styles */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
}

.regular-dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.regular-dropdown-item[aria-selected="true"] {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

/* Multi-field display styling */
.regular-dropdown-item .field-value {
  display: inline-block;
  padding: 2px 6px;
  background-color: #f1f3f4;
  border-radius: 4px;
  font-size: 12px;
  color: #5f6368;
  border: 1px solid #dadce0;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.regular-dropdown-item:hover .field-value {
  background-color: #e8eaed;
  border-color: #c8c9ca;
}

.regular-dropdown-item[aria-selected="true"] .field-value {
  background-color: #bbdefb;
  border-color: #90caf9;
  color: #1565c0;
}

/* First field value (primary) styling */
.regular-dropdown-item .field-value:first-child {
  background-color: #e8f5e8;
  border-color: #c8e6c9;
  color: #2e7d32;
  font-weight: 500;
  max-width: 150px;
}

.regular-dropdown-item:hover .field-value:first-child {
  background-color: #c8e6c9;
  border-color: #a5d6a7;
}

.regular-dropdown-item[aria-selected="true"] .field-value:first-child {
  background-color: #a5d6a7;
  border-color: #81c784;
  color: #1b5e20;
}

.regular-dropdown-empty {
  /* Inherits base dropdown-empty styles */
  color: #757575;
}

/* Regular dropdown input specific styling */
.dropdown-input-container input[type="text"] {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
}

.dropdown-input-container input[type="text"]:focus {
  border-color: #283A97;
  box-shadow: 0 0 0 2px rgba(40, 58, 151, 0.1);
}

/* Loading state specific to regular dropdown */
.regular-dropdown .dropdown-loading {
  color: #283A97;
  font-style: normal;
}

.regular-dropdown .dropdown-loading::after {
  border-color: #283A97;
  border-top-color: transparent;
}

/* Error state specific to regular dropdown */
.regular-dropdown .dropdown-error {
  background-color: #ffebee;
  border-color: #ffcdd2;
  color: #c62828;
}

/* Responsive adjustments for regular dropdown */
@media (max-width: 768px) {
  .regular-dropdown-item {
    font-size: 13px;
    padding: 10px 12px;
    gap: 6px;
  }
  
  .regular-dropdown-item .field-value {
    font-size: 11px;
    padding: 1px 4px;
    max-width: 100px;
  }
  
  .regular-dropdown-item .field-value:first-child {
    max-width: 120px;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .regular-dropdown-item {
    font-size: 12px;
    padding: 8px 10px;
    gap: 4px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .regular-dropdown-item .field-value {
    font-size: 10px;
    padding: 1px 3px;
    max-width: 80px;
  }
  
  .regular-dropdown-item .field-value:first-child {
    max-width: 100px;
    order: -1;
    width: 100%;
    max-width: none;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .regular-dropdown-item {
    border: 1px solid transparent;
  }
  
  .regular-dropdown-item:hover,
  .regular-dropdown-item[aria-selected="true"] {
    border-color: currentColor;
    font-weight: bold;
  }
  
  .regular-dropdown-item .field-value {
    border-width: 2px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .regular-dropdown-item {
    color: #e0e0e0;
  }
  
  .regular-dropdown-item:hover {
    background-color: #424242;
    color: #ffffff;
  }
  
  .regular-dropdown-item[aria-selected="true"] {
    background-color: #1976d2;
    color: #ffffff;
  }
  
  .regular-dropdown-item .field-value {
    background-color: #424242;
    border-color: #616161;
    color: #e0e0e0;
  }
  
  .regular-dropdown-item:hover .field-value {
    background-color: #616161;
    border-color: #757575;
  }
  
  .regular-dropdown-item[aria-selected="true"] .field-value {
    background-color: #1565c0;
    border-color: #1976d2;
    color: #ffffff;
  }
  
  .regular-dropdown-item .field-value:first-child {
    background-color: #2e7d32;
    border-color: #388e3c;
    color: #c8e6c9;
  }
  
  .dropdown-input-container input[type="text"] {
    color: #e0e0e0;
    background-color: #1e1e1e;
    border-color: #424242;
  }
  
  .dropdown-input-container input[type="text"]:focus {
    border-color: #90caf9;
    box-shadow: 0 0 0 2px rgba(144, 202, 249, 0.2);
  }
}

/* Animation for regular dropdown selection */
.regular-dropdown-item {
  transition: all 0.2s ease;
}

.regular-dropdown-item:active {
  transform: scale(0.98);
}

.regular-dropdown-item .field-value {
  transition: all 0.15s ease;
}

/* Keyboard navigation styling */
.regular-dropdown-item:focus {
  outline: 2px solid #283A97;
  outline-offset: -2px;
  background-color: #f8f9fa;
}

/* Ensure proper spacing in form contexts */
.form-field .regular-dropdown {
  margin-top: 0;
}

/* Field value truncation with tooltip support */
.regular-dropdown-item .field-value[title] {
  cursor: help;
}

/* Loading animation enhancement */
.regular-dropdown .dropdown-loading {
  position: relative;
}

.regular-dropdown .dropdown-loading::before {
  content: '📋';
  margin-right: 8px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Multi-field layout optimization */
.regular-dropdown-item.single-field .field-value {
  max-width: none;
  width: 100%;
  text-align: left;
}

.regular-dropdown-item.multi-field {
  min-height: 40px;
  align-items: flex-start;
  padding-top: 8px;
  padding-bottom: 8px;
}

/* Field type indicators (optional) */
.regular-dropdown-item .field-value.numeric {
  background-color: #fff3e0;
  border-color: #ffcc02;
  color: #e65100;
}

.regular-dropdown-item .field-value.date {
  background-color: #e8eaf6;
  border-color: #c5cae9;
  color: #3f51b5;
}

.regular-dropdown-item .field-value.text {
  background-color: #f3e5f5;
  border-color: #e1bee7;
  color: #7b1fa2;
}
