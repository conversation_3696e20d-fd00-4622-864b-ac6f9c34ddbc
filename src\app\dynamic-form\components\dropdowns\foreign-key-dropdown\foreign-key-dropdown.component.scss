/* Foreign Key Dropdown Component Styles */
/* Inherits from base dropdown and adds Foreign Key-specific styling */

@import '../base-dropdown/base-dropdown.component.scss';

/* Foreign Key-specific dropdown styling */
.foreign-key-dropdown {
  /* Inherits all base dropdown styles */
}

.foreign-key-dropdown-item {
  /* Inherits base dropdown-item styles */
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #6a1b9a; /* Purple color for foreign key names */
  position: relative;
}

.foreign-key-dropdown-item:hover {
  background-color: #f3e5f5;
  color: #4a148c;
}

.foreign-key-dropdown-item[aria-selected="true"] {
  background-color: #e1bee7;
  color: #4a148c;
  font-weight: 600;
}

/* Add icon indicator for foreign key items */
.foreign-key-dropdown-item::before {
  content: '🔗';
  margin-right: 8px;
  font-size: 12px;
  opacity: 0.7;
}

.foreign-key-dropdown-empty {
  /* Inherits base dropdown-empty styles */
  color: #757575;
}

/* Foreign key input specific styling */
.dropdown-input-container input[type="text"] {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #6a1b9a;
}

.dropdown-input-container input[type="text"]:focus {
  border-color: #9c27b0;
  box-shadow: 0 0 0 2px rgba(156, 39, 176, 0.1);
}

/* Loading state specific to foreign key dropdown */
.foreign-key-dropdown .dropdown-loading {
  color: #9c27b0;
  font-style: normal;
}

.foreign-key-dropdown .dropdown-loading::after {
  border-color: #9c27b0;
  border-top-color: transparent;
}

/* Error state specific to foreign key dropdown */
.foreign-key-dropdown .dropdown-error {
  background-color: #fce4ec;
  border-color: #f8bbd9;
  color: #c2185b;
}

/* Cache indicator (optional visual feedback) */
.foreign-key-dropdown.cached::before {
  content: '⚡';
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 10px;
  color: #9c27b0;
  z-index: 1001;
}

/* Foreign key category styling (if categories are implemented) */
.foreign-key-dropdown-item.category-form {
  color: #6a1b9a; /* Purple for form definitions */
}

.foreign-key-dropdown-item.category-table {
  color: #1976d2; /* Blue for table references */
}

.foreign-key-dropdown-item.category-view {
  color: #388e3c; /* Green for view references */
}

/* Relationship indicator */
.foreign-key-dropdown-item.one-to-many::after {
  content: '1:N';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #757575;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
}

.foreign-key-dropdown-item.many-to-one::after {
  content: 'N:1';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #757575;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
}

.foreign-key-dropdown-item.many-to-many::after {
  content: 'N:N';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #757575;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 2px;
}

/* Responsive adjustments for foreign key dropdown */
@media (max-width: 768px) {
  .foreign-key-dropdown-item {
    font-size: 13px;
    padding: 10px 12px;
  }
  
  .foreign-key-dropdown-item::before {
    font-size: 11px;
    margin-right: 6px;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .foreign-key-dropdown-item {
    font-size: 12px;
    padding: 8px 10px;
  }
  
  .foreign-key-dropdown-item::before {
    font-size: 10px;
    margin-right: 4px;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 12px;
  }
  
  /* Hide relationship indicators on small screens */
  .foreign-key-dropdown-item::after {
    display: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .foreign-key-dropdown-item {
    border: 1px solid transparent;
  }
  
  .foreign-key-dropdown-item:hover,
  .foreign-key-dropdown-item[aria-selected="true"] {
    border-color: currentColor;
    font-weight: bold;
  }
  
  .foreign-key-dropdown-item::before {
    filter: contrast(2);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .foreign-key-dropdown-item {
    color: #ce93d8;
  }
  
  .foreign-key-dropdown-item:hover {
    background-color: #4a148c;
    color: #f3e5f5;
  }
  
  .foreign-key-dropdown-item[aria-selected="true"] {
    background-color: #6a1b9a;
    color: #f3e5f5;
  }
  
  .dropdown-input-container input[type="text"] {
    color: #ce93d8;
    background-color: #1e1e1e;
    border-color: #424242;
  }
  
  .dropdown-input-container input[type="text"]:focus {
    border-color: #ce93d8;
    box-shadow: 0 0 0 2px rgba(206, 147, 216, 0.2);
  }
  
  .foreign-key-dropdown-item::after {
    background-color: #424242;
    color: #e0e0e0;
  }
}

/* Animation for foreign key selection */
.foreign-key-dropdown-item {
  transition: all 0.2s ease;
}

.foreign-key-dropdown-item:active {
  transform: scale(0.98);
}

/* Keyboard navigation styling */
.foreign-key-dropdown-item:focus {
  outline: 2px solid #9c27b0;
  outline-offset: -2px;
  background-color: #f3e5f5;
}

/* Performance indicator for cached data */
.foreign-key-dropdown.fast-load {
  animation: fastLoadIndicator 0.3s ease-out;
}

@keyframes fastLoadIndicator {
  0% {
    box-shadow: 0 0 0 0 rgba(156, 39, 176, 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(156, 39, 176, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(156, 39, 176, 0);
  }
}

/* Ensure proper spacing in form contexts */
.form-field .foreign-key-dropdown {
  margin-top: 0;
}

/* Foreign key validation styling (if needed) */
.foreign-key-dropdown-item.invalid-reference {
  color: #d32f2f;
  text-decoration: line-through;
  opacity: 0.6;
}

.foreign-key-dropdown-item.deprecated-reference {
  color: #ff9800;
  font-style: italic;
}

.foreign-key-dropdown-item.deprecated-reference::before {
  content: '⚠️';
  margin-right: 4px;
}

/* Loading animation enhancement */
.foreign-key-dropdown .dropdown-loading {
  position: relative;
}

.foreign-key-dropdown .dropdown-loading::before {
  content: '🔗';
  margin-right: 8px;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
