import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { RegularDropdownComponent } from './regular-dropdown.component';
import { environment } from '../../../../../environments/environment';
import { CustomField } from '../../../../core/models/custom-field';

describe('RegularDropdownComponent', () => {
  let component: RegularDropdownComponent;
  let fixture: ComponentFixture<RegularDropdownComponent>;
  let httpMock: HttpTestingController;

  const mockFields: CustomField[] = [
    {
      fieldName: 'testField',
      type: 'string',
      mandatory: false,
      Group: 'test',
      isMulti: false,
      noChange: false,
      foreginKey: 'testForeignKey',
      label: 'Test Field'
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RegularDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(RegularDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    
    // Set required inputs
    component.fieldName = 'testField';
    component.foreignKey = 'testForeignKey';
    component.fields = mockFields;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with Regular dropdown-specific configuration', () => {
    component.ngOnInit();
    
    expect(component.dropdownType).toBe('regular');
    expect(component.config.cacheEnabled).toBe(false);
    expect(component.config.clientSideFiltering).toBe(true);
    expect(component.config.foreignKey).toBe('testForeignKey');
  });

  it('should parse field context correctly', () => {
    component.fieldName = 'testField_nested_1_2';
    component.ngOnInit();
    
    expect(component.fieldContext?.originalFieldName).toBe('testField');
    expect(component.fieldContext?.isNested).toBe(true);
    expect(component.fieldContext?.groupIndex).toBe(1);
    expect(component.fieldContext?.nestedGroupIndex).toBe(2);
  });

  it('should parse multi-field context correctly', () => {
    component.fieldName = 'testField_5';
    component.ngOnInit();
    
    expect(component.fieldContext?.originalFieldName).toBe('testField');
    expect(component.fieldContext?.isMulti).toBe(true);
    expect(component.fieldContext?.multiIndex).toBe(5);
  });

  it('should generate default placeholder from field context', () => {
    component.ngOnInit();
    
    expect(component.config.placeholder).toBe('Search Test Field');
  });

  it('should perform search with correct API call', () => {
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testForeignKey`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      _select: ['ROW_ID'],
      _limit: 50
    });
    
    const mockResponse = [
      { ROW_ID: 'option1', name: 'First Option', description: 'First Description' },
      { ROW_ID: 'option2', name: 'Second Option', description: 'Second Description' }
    ];
    
    req.flush(mockResponse);
    
    expect(component.state.filteredOptions.length).toBe(2);
    expect(component.state.isOpen).toBe(true);
  });

  it('should show error when foreign key is missing', () => {
    component.foreignKey = '';
    component.ngOnInit();
    
    component.performSearch('test');
    
    expect(component.state.error).toBe('Foreign key is required');
    expect(component.state.isOpen).toBe(true);
    httpMock.expectNone(`${environment.baseURL}/api/query-builder/search`);
  });

  it('should filter options client-side', () => {
    component.state.options = [
      { ROW_ID: 'option1', name: 'First Option' },
      { ROW_ID: 'option2', name: 'Second Option' },
      { ROW_ID: 'option3', name: 'Third Option' }
    ];
    component.config.clientSideFiltering = true;
    component.ngOnInit();
    
    component.filterOptions('first');
    
    expect(component.state.filteredOptions.length).toBe(1);
    expect(component.state.filteredOptions[0].ROW_ID).toBe('option1');
  });

  it('should get display text with multiple fields', () => {
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      description: 'Test Description'
    };
    
    expect(component.getOptionDisplayText(option)).toBe('Test Name Test Description');
  });

  it('should get display text with specific display fields', () => {
    component.displayFields = ['name'];
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      description: 'Test Description'
    };
    
    expect(component.getOptionDisplayText(option)).toBe('Test Name');
  });

  it('should fallback to ROW_ID when no other fields available', () => {
    const option = { ROW_ID: 'test-id' };
    
    expect(component.getOptionDisplayText(option)).toBe('test-id');
  });

  it('should extract original field name correctly', () => {
    expect(component.extractOriginalFieldName('field_nested_1_2')).toBe('field');
    expect(component.extractOriginalFieldName('field_5')).toBe('field');
    expect(component.extractOriginalFieldName('simpleField')).toBe('simpleField');
  });

  it('should get keys for option display', () => {
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      description: 'Test Description',
      empty: '',
      nullValue: null
    };
    
    const keys = component.getKeys(option);
    
    expect(keys).toEqual(['name', 'description']);
    expect(keys).not.toContain('ROW_ID');
    expect(keys).not.toContain('empty');
    expect(keys).not.toContain('nullValue');
  });

  it('should get keys with specific display fields', () => {
    component.displayFields = ['name', 'description', 'missing'];
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      description: 'Test Description'
    };
    
    const keys = component.getKeys(option);
    
    expect(keys).toEqual(['name', 'description']);
    expect(keys).not.toContain('missing');
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testForeignKey`);
    req.error(new ErrorEvent('Network error'));
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.error).toBe('Failed to load options');
    expect(component.state.isOpen).toBe(true);
  });

  it('should use client-side filtering when data is already loaded', () => {
    component.state.options = [
      { ROW_ID: 'option1', name: 'First Option' },
      { ROW_ID: 'option2', name: 'Second Option' }
    ];
    spyOn(component, 'filterOptions');
    component.ngOnInit();
    
    component.performSearch('first');
    
    expect(component.filterOptions).toHaveBeenCalledWith('first');
    httpMock.expectNone(`${environment.baseURL}/api/query-builder/search`);
  });

  it('should load all options when search term is empty', () => {
    spyOn(component, 'loadAllOptions');
    component.ngOnInit();
    
    component.performSearch('');
    
    expect(component.loadAllOptions).toHaveBeenCalled();
  });

  it('should get searchable text from all option fields', () => {
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      description: 'Test Description',
      category: 'Test Category'
    };
    
    const searchableText = component.getSearchableText(option);
    
    expect(searchableText).toContain('test-id');
    expect(searchableText).toContain('Test Name');
    expect(searchableText).toContain('Test Description');
    expect(searchableText).toContain('Test Category');
  });

  it('should handle non-array API responses', () => {
    component.ngOnInit();
    
    component.loadAllOptions();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testForeignKey`);
    req.flush({ error: 'Invalid response' }); // Non-array response
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.options).toEqual([]);
  });

  it('should handle grouped field names', () => {
    component.fieldName = 'testField_group_1';
    component.ngOnInit();
    
    expect(component.fieldContext?.isGrouped).toBe(true);
  });

  it('should filter empty and null values from searchable text', () => {
    const option = {
      ROW_ID: 'test-id',
      name: 'Test Name',
      empty: '',
      nullValue: null,
      undefinedValue: undefined
    };
    
    const searchableText = component.getSearchableText(option);
    
    expect(searchableText).toBe('test-id Test Name');
  });
});
