import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { IdDropdownComponent } from './id-dropdown.component';
import { environment } from '../../../../../environments/environment';

describe('IdDropdownComponent', () => {
  let component: IdDropdownComponent;
  let fixture: ComponentFixture<IdDropdownComponent>;
  let httpMock: HttpTestingController;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        IdDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IdDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    
    // Set required inputs
    component.fieldName = 'ID';
    component.tableName = 'testTable';
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with ID-specific configuration', () => {
    component.ngOnInit();
    
    expect(component.dropdownType).toBe('id');
    expect(component.config.placeholder).toBe('Enter ID');
    expect(component.config.cacheEnabled).toBe(false);
    expect(component.config.validationEnabled).toBe(true);
  });

  it('should reset validation on input change', () => {
    spyOn(component, 'setValidationState');
    component.ngOnInit();
    
    component.onInputChange('test-id');
    
    expect(component.setValidationState).toHaveBeenCalledWith(false);
  });

  it('should perform search with correct API call', () => {
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      ID: { CT: 'test' },
      _select: ['ID'],
      _limit: 20
    });
    
    const mockResponse = [
      { ID: 'test-id-1' },
      { ID: 'test-id-2' }
    ];
    
    req.flush(mockResponse);
    
    expect(component.state.filteredOptions.length).toBe(2);
    expect(component.state.filteredOptions[0].ID).toBe('test-id-1');
    expect(component.state.isOpen).toBe(true);
  });

  it('should load all IDs with correct API call', () => {
    component.ngOnInit();
    
    component.loadAllOptions();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      _select: ['ID'],
      _limit: 100
    });
    
    const mockResponse = [
      { ID: 'id-1' },
      { ID: 'id-2' },
      { ID: 'id-3' }
    ];
    
    req.flush(mockResponse);
    
    expect(component.state.filteredOptions.length).toBe(3);
    expect(component.state.options.length).toBe(3);
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
    req.error(new ErrorEvent('Network error'));
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.error).toBe('Failed to load IDs');
    expect(component.state.isOpen).toBe(true);
  });

  it('should extract query builder ID from table name with comma', () => {
    component.tableName = 'testTable,extraInfo';
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
    expect(req.request.url).toContain('queryBuilderId=testTable');
    
    req.flush([]);
  });

  it('should use screenName when tableName is not provided', () => {
    component.tableName = undefined;
    component.screenName = 'testScreen';
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testScreen`);
    expect(req.request.url).toContain('queryBuilderId=testScreen');
    
    req.flush([]);
  });

  it('should show error when no table or screen name provided', () => {
    component.tableName = undefined;
    component.screenName = undefined;
    component.ngOnInit();
    
    component.performSearch('test');
    
    expect(component.state.error).toBe('Table name or screen name is required');
    expect(component.state.isOpen).toBe(true);
    httpMock.expectNone(`${environment.baseURL}/api/query-builder/search`);
  });

  it('should reset validation when valid option is selected', () => {
    spyOn(component, 'setValidationState');
    component.ngOnInit();
    
    const testOption = { ROW_ID: 'test-id', ID: 'test-id' };
    component.selectOption(testOption);
    
    expect(component.setValidationState).toHaveBeenCalledWith(false);
  });

  it('should get correct display text for ID options', () => {
    const option1 = { ROW_ID: 'row-id', ID: 'test-id' };
    const option2 = { ROW_ID: 'fallback-id' };
    
    expect(component.getOptionDisplayText(option1)).toBe('test-id');
    expect(component.getOptionDisplayText(option2)).toBe('fallback-id');
  });

  it('should set validation state and emit events', () => {
    spyOn(component.validationStateChange, 'emit');
    spyOn(component.validationChange, 'emit');
    
    component.setValidationState(true);
    
    expect(component.showValidation).toBe(true);
    expect(component.validationStateChange.emit).toHaveBeenCalledWith(true);
    expect(component.validationChange.emit).toHaveBeenCalledWith({
      isValid: false,
      errorMessage: 'Please select a valid ID'
    });
  });

  it('should apply invalid input class when validation fails', () => {
    component.showValidation = true;
    
    const inputClass = component.getInputClass();
    
    expect(inputClass).toContain('invalid-input');
  });

  it('should validate input correctly', () => {
    component.required = true;
    component.ngOnInit();
    spyOn(component, 'setValidationState');
    
    // Test with empty value
    component.inputControl.setValue('');
    const isValid1 = component.validateInput();
    expect(isValid1).toBe(false);
    expect(component.setValidationState).toHaveBeenCalledWith(true);
    
    // Test with valid value
    component.inputControl.setValue('valid-id');
    const isValid2 = component.validateInput();
    expect(isValid2).toBe(true);
  });

  it('should clear input and reset state', () => {
    component.ngOnInit();
    component.inputControl.setValue('test-value');
    component.state.selectedOption = { ROW_ID: 'test', ID: 'test' };
    component.showValidation = true;
    spyOn(component, 'closeDropdown');
    spyOn(component, 'setValidationState');
    
    component.clearInput();
    
    expect(component.inputControl.value).toBe('');
    expect(component.state.selectedOption).toBeUndefined();
    expect(component.state.searchTerm).toBe('');
    expect(component.setValidationState).toHaveBeenCalledWith(false);
    expect(component.closeDropdown).toHaveBeenCalled();
  });

  it('should handle non-array API responses', () => {
    component.ngOnInit();
    
    component.performSearch('test');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=testTable`);
    req.flush({ error: 'Invalid response' }); // Non-array response
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.isOpen).toBe(true);
  });

  it('should emit validation state changes', () => {
    spyOn(component.validationStateChange, 'emit');
    component.validationEnabled = true;
    
    component.setValidationState(true);
    
    expect(component.validationStateChange.emit).toHaveBeenCalledWith(true);
  });
});
