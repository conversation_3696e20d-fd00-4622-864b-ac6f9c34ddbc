<div [class]="getContainerClass()">
  <!-- Input field with search functionality -->
  <input 
    [formControl]="inputControl"
    [id]="fieldName" 
    type="text" 
    [class]="getInputClass()"
    (focus)="onInputFocus()" 
    (blur)="onInputBlur()"
    [disabled]="disabled || readonly"
    [placeholder]="placeholder"
    [attr.aria-label]="config.accessibility?.ariaLabel || placeholder"
    [attr.aria-describedby]="config.accessibility?.ariaDescribedBy"
    [attr.role]="config.accessibility?.role || 'combobox'"
    [attr.aria-expanded]="state.isOpen"
    [attr.aria-autocomplete]="'list'"
    [attr.tabindex]="config.accessibility?.tabIndex || 0" />
  
  <!-- Arrow button to toggle dropdown -->
  <button 
    type="button" 
    class="dropdown-arrow-btn" 
    (click)="toggleDropdown()" 
    [disabled]="disabled || readonly"
    [matTooltip]="'Show options'"
    [attr.aria-label]="'Toggle dropdown options'">
    <mat-icon>{{ state.isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
  </button>
  
  <!-- Dropdown list for filtered results -->
  @if (state.isOpen) {
    <div class="dropdown-list regular-dropdown" [class]="config.theme?.listClass || ''">
      <!-- Loading state -->
      @if (state.isLoading) {
        <div class="dropdown-loading" [class]="config.theme?.loadingClass || ''">
          {{ config.loadingMessage || 'Loading options...' }}
        </div>
      }
      
      <!-- Regular options list with multi-field display -->
      @else if (state.filteredOptions && state.filteredOptions.length > 0) {
        @for (option of state.filteredOptions; track trackByOptionId($index, option)) {
          <div 
            class="dropdown-item regular-dropdown-item" 
            [class]="config.theme?.itemClass || ''"
            (click)="selectOption(option)"
            [attr.role]="'option'"
            [attr.aria-selected]="state.selectedOption?.ROW_ID === option.ROW_ID">
            
            <!-- Multi-field display -->
            @if (displayFields && displayFields.length > 0) {
              @for (field of displayFields; track field) {
                @if (option[field]) {
                  <span class="field-value">{{ option[field] }}</span>
                }
              }
            } @else {
              <!-- Default display: all non-ROW_ID fields -->
              @for (key of getKeys(option); track key) {
                <span class="field-value">{{ option[key] }}</span>
              }
            }
          </div>
        }
      }
      
      <!-- Empty state -->
      @else {
        <div class="dropdown-empty regular-dropdown-empty" [class]="config.theme?.emptyClass || ''">
          {{ config.emptyMessage || 'No options found' }}
        </div>
      }
      
      <!-- Error state -->
      @if (state.error) {
        <div class="dropdown-error" [class]="config.theme?.errorClass || ''">
          {{ state.error }}
        </div>
      }
    </div>
  }
</div>
