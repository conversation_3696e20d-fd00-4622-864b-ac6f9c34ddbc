/* Type Dropdown Component Styles */
/* Inherits from base dropdown and adds Type-specific styling */

@import '../base-dropdown/base-dropdown.component.scss';

/* Type-specific dropdown styling */
.type-dropdown {
  /* Inherits all base dropdown styles */
}

.type-dropdown-item {
  /* Inherits base dropdown-item styles */
  font-family: 'Courier New', monospace; /* Monospace for type names */
  font-size: 13px;
  font-weight: 500;
  color: #2e7d32; /* Green color for type names */
}

.type-dropdown-item:hover {
  background-color: #e8f5e8;
  color: #1b5e20;
}

.type-dropdown-item[aria-selected="true"] {
  background-color: #c8e6c9;
  color: #1b5e20;
  font-weight: 600;
}

.type-dropdown-empty {
  /* Inherits base dropdown-empty styles */
  color: #757575;
}

/* Type input specific styling */
.dropdown-input-container input[type="text"] {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #2e7d32;
}

.dropdown-input-container input[type="text"]:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

/* Loading state specific to type dropdown */
.type-dropdown .dropdown-loading {
  color: #4caf50;
  font-style: normal;
}

.type-dropdown .dropdown-loading::after {
  border-color: #4caf50;
  border-top-color: transparent;
}

/* Error state specific to type dropdown */
.type-dropdown .dropdown-error {
  background-color: #fff3e0;
  border-color: #ffcc02;
  color: #e65100;
}

/* Cache indicator (optional visual feedback) */
.type-dropdown.cached::before {
  content: '⚡';
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 10px;
  color: #4caf50;
  z-index: 1001;
}

/* Type category styling (if categories are implemented) */
.type-dropdown-item.category-primitive {
  color: #1976d2; /* Blue for primitive types */
}

.type-dropdown-item.category-object {
  color: #7b1fa2; /* Purple for object types */
}

.type-dropdown-item.category-array {
  color: #f57c00; /* Orange for array types */
}

/* Responsive adjustments for type dropdown */
@media (max-width: 768px) {
  .type-dropdown-item {
    font-size: 12px;
    padding: 10px 12px;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .type-dropdown-item {
    font-size: 11px;
    padding: 8px 10px;
  }
  
  .dropdown-input-container input[type="text"] {
    font-size: 11px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .type-dropdown-item {
    border: 1px solid transparent;
  }
  
  .type-dropdown-item:hover,
  .type-dropdown-item[aria-selected="true"] {
    border-color: currentColor;
    font-weight: bold;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .type-dropdown-item {
    color: #81c784;
  }
  
  .type-dropdown-item:hover {
    background-color: #2e7d32;
    color: #c8e6c9;
  }
  
  .type-dropdown-item[aria-selected="true"] {
    background-color: #388e3c;
    color: #e8f5e8;
  }
  
  .dropdown-input-container input[type="text"] {
    color: #81c784;
    background-color: #1e1e1e;
    border-color: #424242;
  }
  
  .dropdown-input-container input[type="text"]:focus {
    border-color: #81c784;
    box-shadow: 0 0 0 2px rgba(129, 199, 132, 0.2);
  }
}

/* Animation for type selection */
.type-dropdown-item {
  transition: all 0.2s ease;
}

.type-dropdown-item:active {
  transform: scale(0.98);
}

/* Keyboard navigation styling */
.type-dropdown-item:focus {
  outline: 2px solid #4caf50;
  outline-offset: -2px;
  background-color: #e8f5e8;
}

/* Performance indicator for cached data */
.type-dropdown.fast-load {
  animation: fastLoadIndicator 0.3s ease-out;
}

@keyframes fastLoadIndicator {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* Ensure proper spacing in form contexts */
.form-field .type-dropdown {
  margin-top: 0;
}

/* Type validation styling (if needed) */
.type-dropdown-item.invalid-type {
  color: #d32f2f;
  text-decoration: line-through;
  opacity: 0.6;
}

.type-dropdown-item.deprecated-type {
  color: #ff9800;
  font-style: italic;
}

.type-dropdown-item.deprecated-type::after {
  content: ' (deprecated)';
  font-size: 10px;
  color: #757575;
}
