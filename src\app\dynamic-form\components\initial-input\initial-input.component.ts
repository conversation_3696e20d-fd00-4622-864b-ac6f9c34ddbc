import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

// Import new dropdown components
import { IdDropdownComponent } from '../dropdowns/id-dropdown/id-dropdown.component';
import { DropdownEventData, DropdownValidationResult } from '../../../core/models/dropdown.models';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    IdDropdownComponent
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;

  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  private http = inject(HttpClient);

  ngOnInit() {
    // Component initialization
  }

  ngOnDestroy() {
    // Component cleanup
  }

  /**
   * Handle ID dropdown value changes
   */
  onIdValueChange(eventData: DropdownEventData): void {
    if (eventData.fieldName === 'ID') {
      this.form.get('ID')?.setValue(eventData.value);

      // Reset validation when value changes
      if (eventData.value && eventData.value.trim() !== '') {
        this.showValidation = false;
        this.validationChange.emit(false);
      }
    }
  }

  /**
   * Handle ID dropdown validation changes
   */
  onIdValidationChange(validationResult: DropdownValidationResult): void {
    this.showValidation = !validationResult.isValid;
    this.validationChange.emit(!validationResult.isValid);
  }

  /**
   * Get input class for styling
   */
  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }



  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
  }
}
