import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { TypeDropdownComponent } from './type-dropdown.component';
import { environment } from '../../../../../environments/environment';

describe('TypeDropdownComponent', () => {
  let component: TypeDropdownComponent;
  let fixture: ComponentFixture<TypeDropdownComponent>;
  let httpMock: HttpTestingController;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TypeDropdownComponent,
        ReactiveFormsModule,
        HttpClientTestingModule,
        MatButtonModule,
        MatIconModule,
        MatTooltipModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TypeDropdownComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    
    // Set required inputs
    component.fieldName = 'typeField';
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with Type-specific configuration', () => {
    component.ngOnInit();
    
    expect(component.dropdownType).toBe('type');
    expect(component.config.placeholder).toBe('Search field types');
    expect(component.config.cacheEnabled).toBe(true);
    expect(component.config.clientSideFiltering).toBe(true);
    expect(component.config.searchDelay).toBe(200);
  });

  it('should preload type options on initialization', () => {
    component.ngOnInit();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual({
      _select: ['ROW_ID']
    });
    
    const mockResponse = [
      { ROW_ID: 'string' },
      { ROW_ID: 'int' },
      { ROW_ID: 'boolean' }
    ];
    
    req.flush(mockResponse);
    
    expect(component.apiCache['fieldType']).toBeDefined();
    expect(component.apiCache['fieldType'].data.length).toBe(3);
  });

  it('should use cached data when available', () => {
    // Pre-populate cache
    component.apiCache['fieldType'] = {
      data: [
        { ROW_ID: 'string' },
        { ROW_ID: 'int' }
      ],
      timestamp: Date.now(),
      expiresIn: 30 * 60 * 1000
    };
    
    component.ngOnInit();
    component.loadAllOptions();
    
    expect(component.state.filteredOptions.length).toBe(2);
    expect(component.state.isOpen).toBe(true);
    
    // Should not make HTTP request when using cache
    httpMock.expectNone(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
  });

  it('should perform client-side filtering with cached data', () => {
    // Pre-populate cache
    component.apiCache['fieldType'] = {
      data: [
        { ROW_ID: 'string' },
        { ROW_ID: 'int' },
        { ROW_ID: 'boolean' }
      ],
      timestamp: Date.now(),
      expiresIn: 30 * 60 * 1000
    };
    
    component.ngOnInit();
    component.performSearch('str');
    
    expect(component.state.filteredOptions.length).toBe(1);
    expect(component.state.filteredOptions[0].ROW_ID).toBe('string');
    expect(component.state.isOpen).toBe(true);
  });

  it('should load and filter when cache is not available', () => {
    component.ngOnInit();
    component.performSearch('int');
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    
    const mockResponse = [
      { ROW_ID: 'string' },
      { ROW_ID: 'int' },
      { ROW_ID: 'boolean' }
    ];
    
    req.flush(mockResponse);
    
    expect(component.state.filteredOptions.length).toBe(1);
    expect(component.state.filteredOptions[0].ROW_ID).toBe('int');
    expect(component.apiCache['fieldType']).toBeDefined();
  });

  it('should handle API errors gracefully', () => {
    component.ngOnInit();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    req.error(new ErrorEvent('Network error'));
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.error).toBe('Failed to load field types');
  });

  it('should get correct display text for type options', () => {
    const option = { ROW_ID: 'string' };
    
    expect(component.getOptionDisplayText(option)).toBe('string');
  });

  it('should clear cache', () => {
    component.apiCache['fieldType'] = {
      data: [{ ROW_ID: 'test' }],
      timestamp: Date.now(),
      expiresIn: 30 * 60 * 1000
    };
    
    component.clearCache();
    
    expect(component.apiCache['fieldType']).toBeUndefined();
  });

  it('should refresh data by clearing cache and reloading', () => {
    spyOn(component, 'clearCache');
    spyOn(component, 'loadTypesFromApi');
    
    component.refreshData();
    
    expect(component.clearCache).toHaveBeenCalled();
    expect(component.loadTypesFromApi).toHaveBeenCalled();
  });

  it('should check cache validity correctly', () => {
    const now = Date.now();
    
    // Valid cache
    component.apiCache['fieldType'] = {
      data: [{ ROW_ID: 'test' }],
      timestamp: now,
      expiresIn: 30 * 60 * 1000
    };
    
    expect(component.isCacheValid()).toBe(true);
    
    // Expired cache
    component.apiCache['fieldType'] = {
      data: [{ ROW_ID: 'test' }],
      timestamp: now - (31 * 60 * 1000), // 31 minutes ago
      expiresIn: 30 * 60 * 1000
    };
    
    expect(component.isCacheValid()).toBe(false);
    
    // No cache
    delete component.apiCache['fieldType'];
    expect(component.isCacheValid()).toBe(false);
  });

  it('should handle non-array API responses', () => {
    component.ngOnInit();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    req.flush({ error: 'Invalid response' }); // Non-array response
    
    expect(component.state.filteredOptions).toEqual([]);
    expect(component.state.options).toEqual([]);
  });

  it('should not open dropdown when preloading', () => {
    component.ngOnInit();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    req.flush([{ ROW_ID: 'string' }]);
    
    // Should not open dropdown during preload
    expect(component.state.isOpen).toBe(false);
  });

  it('should load all options when search term is empty', () => {
    spyOn(component, 'loadAllOptions');
    component.ngOnInit();
    
    component.performSearch('');
    
    expect(component.loadAllOptions).toHaveBeenCalled();
  });

  it('should cache data with correct expiration time', () => {
    component.ngOnInit();
    
    const req = httpMock.expectOne(`${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`);
    const beforeTime = Date.now();
    
    req.flush([{ ROW_ID: 'string' }]);
    
    const afterTime = Date.now();
    const cachedData = component.apiCache['fieldType'];
    
    expect(cachedData.timestamp).toBeGreaterThanOrEqual(beforeTime);
    expect(cachedData.timestamp).toBeLessThanOrEqual(afterTime);
    expect(cachedData.expiresIn).toBe(30 * 60 * 1000); // 30 minutes
  });

  it('should filter case-insensitively', () => {
    component.apiCache['fieldType'] = {
      data: [
        { ROW_ID: 'String' },
        { ROW_ID: 'INTEGER' },
        { ROW_ID: 'boolean' }
      ],
      timestamp: Date.now(),
      expiresIn: 30 * 60 * 1000
    };
    
    component.ngOnInit();
    component.performSearch('string');
    
    expect(component.state.filteredOptions.length).toBe(1);
    expect(component.state.filteredOptions[0].ROW_ID).toBe('String');
  });
});
