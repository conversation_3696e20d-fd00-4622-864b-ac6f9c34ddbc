/* Base Dropdown Component Styles */
/* Extracted and consolidated from existing dropdown implementations */

/* Dropdown Input Container - EXACT from regular-field component lines 134-151 */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.dropdown-input-container .form-input {
  flex: 1;
  padding-right: 40px; /* Space for arrow button */
  border-radius: 8px 0 0 8px;
  border-right: none;
}

.dropdown-input-container .dropdown-arrow-btn {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
  height: 100%;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  z-index: 1;
}

.dropdown-input-container .dropdown-arrow-btn:hover:not(:disabled) {
  background-color: #e9ecef;
}

.dropdown-input-container .dropdown-arrow-btn:disabled {
  background-color: #f1f3f4;
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #495057;
}

/* Dropdown List Styles - EXACT from regular-field component lines 154-193 */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.dropdown-loading {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dropdown-error {
  padding: 12px 16px;
  color: #dc3545;
  font-style: italic;
  text-align: center;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Form Input Base Styles */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  background-color: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #283A97;
  box-shadow: 0 0 0 2px rgba(40, 58, 151, 0.1);
}

.form-input:disabled,
.form-input:read-only {
  background-color: #f1f3f4;
  color: #5f6368;
  cursor: not-allowed;
  border-color: #dadce0;
  opacity: 1;
}

.form-input.disabled {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  cursor: not-allowed !important;
  border-color: #dadce0 !important;
  opacity: 1 !important;
  pointer-events: none !important;
}

/* Dropdown Input Specific Styles */
.dropdown-input {
  cursor: pointer;
}

.dropdown-input:disabled,
.dropdown-input:read-only {
  cursor: not-allowed;
}

/* Scrollbar styling for dropdown lists - EXACT from regular-field component lines 494-510 */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive dropdown styling - EXACT from regular-field component lines 427-491 */
@media (max-width: 768px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty,
  .dropdown-loading,
  .dropdown-error {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty,
  .dropdown-loading,
  .dropdown-error {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Ensure dropdowns don't overflow in small containers */
.dropdown-input-container {
  max-width: 100%;
  overflow: visible;
}

/* Loading state animation */
.dropdown-loading::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  border: 2px solid #6c757d;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Accessibility improvements */
.dropdown-item[aria-selected="true"] {
  background-color: #e3f2fd;
  color: #1976d2;
}

.dropdown-item:focus {
  outline: 2px solid #283A97;
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dropdown-list {
    border: 2px solid;
  }
  
  .dropdown-item:hover,
  .dropdown-item[aria-selected="true"] {
    outline: 2px solid;
  }
}
