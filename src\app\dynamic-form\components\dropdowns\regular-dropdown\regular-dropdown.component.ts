import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../../environments/environment';

// Import base dropdown and models
import { BaseDropdownComponent } from '../base-dropdown/base-dropdown.component';
import {
  DropdownOption,
  RegularDropdownConfig,
  DropdownFieldContext
} from '../../../../core/models/dropdown.models';
import { CustomField } from '../../../../core/models/custom-field';

@Component({
  selector: 'app-regular-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './regular-dropdown.component.html',
  styleUrl: './regular-dropdown.component.scss'
})
export class RegularDropdownComponent extends BaseDropdownComponent implements OnInit {
  @Input() foreignKey!: string; // Required for regular dropdowns
  @Input() fields: CustomField[] = []; // All fields for context
  @Input() displayFields?: string[]; // Fields to display in dropdown options
  @Input() valueField?: string; // Field to use as the value
  @Input() override config: RegularDropdownConfig = {
    foreignKey: ''
  };

  // Field context for complex field name handling
  fieldContext?: DropdownFieldContext;

  override ngOnInit() {
    // Set dropdown type
    this.dropdownType = 'regular';
    
    // Set default configuration for Regular dropdown
    this.config = {
      searchEnabled: true,
      cacheEnabled: false, // Regular dropdowns are dynamic, don't cache by default
      clientSideFiltering: true, // Use client-side filtering after loading
      limit: 50,
      placeholder: this.getDefaultPlaceholder(),
      emptyMessage: 'No options found',
      loadingMessage: 'Loading options...',
      searchDelay: 300,
      displayFields: this.displayFields,
      valueField: this.valueField,
      ...this.config,
      foreignKey: this.foreignKey
    };

    // Parse field context from field name
    this.parseFieldContext();

    super.ngOnInit();
  }

  /**
   * Override perform search for Regular dropdown-specific logic
   */
  protected override performSearch(searchTerm: string): void {
    if (!this.foreignKey) {
      this.state.error = 'Foreign key is required';
      this.state.isOpen = true;
      return;
    }

    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Use client-side filtering if data is already loaded
    if (this.state.options.length > 0) {
      this.filterOptions(searchTerm);
    } else {
      // Load all and then filter
      this.loadAllAndFilter(searchTerm);
    }
  }

  /**
   * Override load all options for Regular dropdown-specific API calls
   */
  protected override loadAllOptions(): void {
    if (!this.foreignKey) {
      this.state.error = 'Foreign key is required';
      this.state.isOpen = true;
      return;
    }

    this.state.isLoading = true;
    this.state.error = undefined;

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${this.foreignKey}`;
    const payload = {
      _select: ["ROW_ID"],
      _limit: this.config.limit || 50
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        
        if (Array.isArray(response)) {
          const options = response.map(item => ({
            ROW_ID: item.ROW_ID,
            ...item
          }));

          this.state.filteredOptions = options;
          this.state.options = [...options];
        } else {
          this.state.filteredOptions = [];
          this.state.options = [];
        }

        this.state.isOpen = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.options = [];
        this.state.error = 'Failed to load options';
        this.state.isOpen = true;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Load all options and filter them
   */
  private loadAllAndFilter(searchTerm: string): void {
    this.state.isLoading = true;
    this.state.error = undefined;

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${this.foreignKey}`;
    const payload = {
      _select: ["ROW_ID"],
      _limit: this.config.limit || 50
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        this.state.isLoading = false;
        
        if (Array.isArray(response)) {
          const options = response.map(item => ({
            ROW_ID: item.ROW_ID,
            ...item
          }));

          this.state.options = options;
          
          // Filter the results
          this.filterOptions(searchTerm);
        } else {
          this.state.filteredOptions = [];
          this.state.options = [];
        }

        this.state.isOpen = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.state.isLoading = false;
        this.state.filteredOptions = [];
        this.state.options = [];
        this.state.error = 'Failed to load options';
        this.state.isOpen = true;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Override filter options to handle multi-field display
   */
  protected override filterOptions(searchTerm: string): void {
    if (!this.config.clientSideFiltering) return;

    const term = searchTerm.toLowerCase();
    this.state.filteredOptions = this.state.options.filter(option => {
      // Search in all available fields of the option
      const searchableText = this.getSearchableText(option).toLowerCase();
      return searchableText.includes(term);
    });

    this.state.isOpen = true;
    this.cdr.detectChanges();
  }

  /**
   * Get display text for Regular dropdown options (multi-field support)
   */
  protected override getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // If specific display fields are configured, use them
    if (this.displayFields && this.displayFields.length > 0) {
      const displayParts = this.displayFields
        .map(field => option[field])
        .filter(value => value !== undefined && value !== null && value !== '')
        .map(value => String(value));

      return displayParts.join(' ');
    }

    // Default behavior: show all non-ROW_ID fields
    const keys = Object.keys(option).filter(key => key !== 'ROW_ID');
    if (keys.length > 0) {
      const displayParts = keys
        .map(key => option[key])
        .filter(value => value !== undefined && value !== null && value !== '')
        .map(value => String(value));

      return displayParts.join(' ');
    }

    // Fallback to ROW_ID
    return option.ROW_ID || '';
  }

  /**
   * Get searchable text from option (includes all fields)
   */
  private getSearchableText(option: DropdownOption): string {
    const allValues = Object.values(option)
      .filter(value => value !== undefined && value !== null && value !== '')
      .map(value => String(value));

    return allValues.join(' ');
  }

  /**
   * Get default placeholder based on field context
   */
  private getDefaultPlaceholder(): string {
    if (this.fieldContext?.originalFieldName) {
      const field = this.fields.find(f => f.fieldName === this.fieldContext?.originalFieldName);
      if (field?.label) {
        return `Search ${field.label}`;
      }
      return `Search ${this.fieldContext.originalFieldName}`;
    }
    return 'Search options';
  }

  /**
   * Parse field context from complex field names
   */
  private parseFieldContext(): void {
    if (!this.fieldName) return;

    this.fieldContext = {
      originalFieldName: this.fieldName,
      displayFieldName: this.fieldName,
      isNested: false,
      isMulti: false,
      isGrouped: false
    };

    // Handle nested group fields: fieldName_nested_k_n
    if (this.fieldName.includes('_nested_')) {
      const parts = this.fieldName.split('_nested_');
      this.fieldContext.originalFieldName = parts[0];
      this.fieldContext.isNested = true;

      if (parts[1]) {
        const indices = parts[1].split('_');
        if (indices.length >= 2) {
          this.fieldContext.groupIndex = parseInt(indices[0]);
          this.fieldContext.nestedGroupIndex = parseInt(indices[1]);
        }
      }
    }
    // Handle multi fields: fieldName_k
    else if (this.fieldName.includes('_') && /\d+$/.test(this.fieldName)) {
      const lastUnderscoreIndex = this.fieldName.lastIndexOf('_');
      const potentialIndex = this.fieldName.substring(lastUnderscoreIndex + 1);

      if (/^\d+$/.test(potentialIndex)) {
        this.fieldContext.originalFieldName = this.fieldName.substring(0, lastUnderscoreIndex);
        this.fieldContext.multiIndex = parseInt(potentialIndex);
        this.fieldContext.isMulti = true;
      }
    }
    // Handle grouped fields
    else if (this.fieldName.includes('_group_')) {
      this.fieldContext.isGrouped = true;
      // Additional parsing for grouped fields if needed
    }
  }

  /**
   * Extract original field name from complex field names
   */
  extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    }

    if (fieldName.includes('_') && /\d+$/.test(fieldName)) {
      const lastUnderscoreIndex = fieldName.lastIndexOf('_');
      const potentialIndex = fieldName.substring(lastUnderscoreIndex + 1);

      if (/^\d+$/.test(potentialIndex)) {
        return fieldName.substring(0, lastUnderscoreIndex);
      }
    }

    return fieldName;
  }

  /**
   * Get keys for option display (excluding ROW_ID)
   */
  getKeys(option: DropdownOption): string[] {
    if (!option) return [];

    // If display fields are specified, use them
    if (this.displayFields && this.displayFields.length > 0) {
      return this.displayFields.filter(field =>
        option[field] !== undefined &&
        option[field] !== null &&
        option[field] !== ''
      );
    }

    // Otherwise, return all keys except ROW_ID
    return Object.keys(option).filter(key =>
      key !== 'ROW_ID' &&
      option[key] !== undefined &&
      option[key] !== null &&
      option[key] !== ''
    );
  }
}
