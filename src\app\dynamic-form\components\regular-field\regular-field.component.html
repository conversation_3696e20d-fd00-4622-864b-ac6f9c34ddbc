<!-- Regular Field (non-grouped, non-multi) - EXACT from main component lines 54-198 - FormGroup Added -->
<div class="form-field" [formGroup]="form">
  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
@if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
 </label>

    @if (field.foreginKey) {
      <!-- Check if this is a type field (fieldType) -->
      @if (field.foreginKey === 'fieldType') {
        <app-type-dropdown
          [fieldName]="field.fieldName"
          [value]="form.get(field.fieldName)?.value"
          [disabled]="isViewMode || field.noInput"
          [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)"
          (valueChange)="onDropdownValueChange($event)">
        </app-type-dropdown>
      }
      <!-- Check if this is a foreign key field (formDefinition) -->
      @else if (field.foreginKey === 'formDefinition') {
        <app-foreign-key-dropdown
          [fieldName]="field.fieldName"
          [value]="form.get(field.fieldName)?.value"
          [disabled]="isViewMode || field.noInput"
          [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)"
          (valueChange)="onDropdownValueChange($event)">
        </app-foreign-key-dropdown>
      }
      <!-- Default dropdown for other foreign keys -->
      @else {
        <app-regular-dropdown
          [fieldName]="field.fieldName"
          [value]="form.get(field.fieldName)?.value"
          [foreignKey]="field.foreginKey"
          [fields]="fields"
          [disabled]="isViewMode || field.noInput"
          [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)"
          (valueChange)="onDropdownValueChange($event)">
        </app-regular-dropdown>
      }
    } @else {
      <!-- Regular input fields for non-foreign key fields -->
      @if (field.type === 'boolean') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'string') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
      }
      @if (field.type === 'int') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'date') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'double') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
    }
  </div>
